<?php
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
global $SESSION_id_rol, $SESSION_id_utilizator, $SESSION_id_instanta;
$dbConnection = DatabasePool::getConnection();

$search = $_POST['search']['value'] ?? '';
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 0;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 10;

$orderColumnIndex = $_POST['order'][0]['column'] ?? 0;
$orderDir = $_POST['order'][0]['dir'] ?? 'ASC';

$columns = [
    0 => "u.utilizator",    // Coloana User
    1 => "zi.den",         // Coloana Instanță
    2 => "zrl.tip"         // Coloana Rol
];
$orderColumn = $columns[$orderColumnIndex] ?? 'u.utilizator';
$orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

$where = "1 = 1";

// Admin sees everything, other roles see based on hierarchy
if ($SESSION_id_rol != 1) {
    $where .= " AND u.idInstanta IN (
        WITH RECURSIVE instance_hierarchy AS (
            -- Base case: start with the current instance
            SELECT id, id_instanta_sup, id_nivel
            FROM exp_jud.z_instante
            WHERE id = :current_instance

            UNION ALL

            -- Recursive part: get subordinate instances based on level
            SELECT i.id, i.id_instanta_sup, i.id_nivel
            FROM exp_jud.z_instante i
            INNER JOIN instance_hierarchy ih ON i.id_instanta_sup = ih.id
            WHERE ih.id_nivel <= 2  -- Only recurse for levels 1 and 2
        )
        SELECT id FROM instance_hierarchy
    )";
}

// Search filtering
$search = str_replace(['\\', '&amp;', '&amp'], '%', sanitizeInput(injSql($_POST['search']['value'])));
if (!empty($search)) {
    $where .= " AND (u.utilizator LIKE '%$search%' OR zi.den LIKE '%$search%')";
}

$filterInstanta = '';
if (isset($_POST['columns'][1]['search']['value']) && $_POST['columns'][1]['search']['value'] !== '') {
    $filterInstanta = str_replace(['\\', '&amp;', '&amp'], '%', sanitizeInput(preg_replace('/\D/', '', $_POST['columns'][1]['search']['value'])));
    if ($filterInstanta === '00') {
        $where .= " AND u.idInstanta is NULL";
    }
    else if ($filterInstanta != '') {
        $where .= " AND zi.id = '$filterInstanta'";
    }
}

// Count total records (for admin show all, for others show filtered)
if ($SESSION_id_rol == 1) {
    $totalQuery = "SELECT COUNT(*) AS total FROM exp_jud.utilizatori u";
    $totalRow = $dbConnection->query($totalQuery)->fetch();
    $totalRecords = $totalRow['total'];
} else {
    $totalQuery = "SELECT COUNT(*) AS total
                   FROM exp_jud.utilizatori u
                   WHERE u.idInstanta IN (
                       WITH RECURSIVE instance_hierarchy AS (
                           SELECT id, id_instanta_sup, id_nivel
                           FROM exp_jud.z_instante
                           WHERE id = :current_instance

                           UNION ALL

                           SELECT i.id, i.id_instanta_sup, i.id_nivel
                           FROM exp_jud.z_instante i
                           INNER JOIN instance_hierarchy ih ON i.id_instanta_sup = ih.id
                           WHERE ih.id_nivel <= 2
                       )
                       SELECT id FROM instance_hierarchy
                   )";
    $stmt = $dbConnection->prepare($totalQuery);
    $stmt->bindValue(':current_instance', $SESSION_id_instanta, PDO::PARAM_INT);
    $stmt->execute();
    $totalRow = $stmt->fetch();
    $totalRecords = $totalRow['total'];
}

// Count filtered records
$filteredRecords = 0;
if (!empty($where)) {
    $filterQuery = "SELECT COUNT(*) AS total
                   FROM exp_jud.utilizatori u
                   JOIN exp_jud.z_instante zi ON zi.id = u.idInstanta
                    LEFT JOIN exp_jud.z_sectii s ON s.id = u.idSectieInstanta
                   JOIN exp_jud.z_rol_utilizator zrl ON zrl.id = u.idRol
                   WHERE $where";

    $stmt = $dbConnection->prepare($filterQuery);
    if ($SESSION_id_rol != 1) {
        $stmt->bindValue(':current_instance', $SESSION_id_instanta, PDO::PARAM_INT);
    }
    $stmt->execute();
    $filterResult = $stmt->fetch();
    $filteredRecords = $filterResult['total'];

    log_search_operation($dbConnection, 'administrare_utilizatori', [$where], [
        'description' => "Search input: $search"
    ]);

}

// Get data
$dataQuery = "SELECT u.*, zi.den instanta, zi.id id_instanta, zrl.tip tip_user, s.denumire sectie,
              CASE WHEN u.dataInactivare IS NOT NULL THEN 1 ELSE 0 END as is_inactive
              FROM exp_jud.utilizatori u
              left JOIN exp_jud.z_instante zi ON zi.id = u.idInstanta
              LEFT JOIN exp_jud.z_sectii s ON s.id = u.idSectieInstanta
              JOIN exp_jud.z_rol_utilizator zrl ON zrl.id = u.idRol
              WHERE $where
              ORDER BY 
                  CASE WHEN u.idInstanta IS NULL THEN 1 ELSE 0 END,
                  $orderColumn $orderDir
              LIMIT :length OFFSET :start";
$stmt = $dbConnection->prepare($dataQuery);
if ($SESSION_id_rol != 1) {
    $stmt->bindValue(':current_instance', $SESSION_id_instanta, PDO::PARAM_INT);
}
$stmt->bindValue(':length', (int)$length, PDO::PARAM_INT);
$stmt->bindValue(':start', (int)$start, PDO::PARAM_INT);
$stmt->execute();
$dataStmt = $stmt->fetchAll();

$data = [];
foreach ($dataStmt as $row) {
    $utilizatorNume = htmlspecialchars($row['utilizator'] ?? '');
    $isInactive = $row['is_inactive'];
    $sectia = $row['sectie'] ? "<br>Sectia: " . htmlspecialchars($row['sectie'] ?? '') : '';

    // Add inactive class and strike-through for inactive users
    $utilizatorNumeTD = $isInactive || $row['idInstanta'] == null
        ? "<b class='text-muted'><s>$utilizatorNume</s></b>"
        : "<b>$utilizatorNume</b>$sectia";

    $utilizatorInstanta = htmlspecialchars($row['instanta'] ?? '');
    $utilizatorRol = htmlspecialchars($row['tip_user'] ?? '');
    if ($utilizatorInstanta == '') {
        $utilizatorInstanta = '<span class="text-muted">Neasignat</span>';
        $utilizatorRol = "<span class='text-muted'>$utilizatorRol</span>";
    }

    // Change actions based on active/inactive status
    if ($isInactive) {
        $utilizatorAction = "
        <button class='btn btn-sm btn-outline-success btn-asignate-user'
                data-bs-toggle='tooltip'
                data-bs-html='true'
                data-bs-placement='left'
                title='Activare cont'
                data-user-id='" . $row['id'] . "'
                data-username='" . htmlspecialchars($row['utilizator']) . "'>
            <i class='fa fa-user-check'></i>
        </button>";
    } else {
        // Check if this is the current logged-in user
        if ($row['id'] == $SESSION_id_utilizator) {
            $utilizatorAction = "<span class='text-muted'><i>Cont curent</i></span>";
        } elseif ($row['idInstanta'] == null && $row['idSectieInstanta' == null]) {
            $utilizatorAction = "
            <button class='btn btn-sm btn-outline-warning btn-edit-user'
                    data-bs-toggle='tooltip'
                    data-bs-html='true'
                    data-bs-placement='left'
                    title='Editare informații utilizator neasignat'
                    data-user-id='" . $row['id'] . "'
                    data-user-info='" . htmlspecialchars(json_encode([
                    'utilizator' => $row['utilizator'],
                    'id_instanta' => $row['id_instanta'],
                    'id_sectie' => $row['idSectieInstanta'],
                    'id_rol' => $row['idRol']
                ])) . "'>
                <i class='fa fa-user-edit'></i>
            </button>";
        } else {
            $utilizatorAction = "
            <button class='btn btn-sm btn-outline-primary btn-edit-user'
                    data-bs-toggle='tooltip'
                    data-bs-html='true'
                    data-bs-placement='left'
                    title='Editare informații'
                    data-user-id='" . $row['id'] . "'
                    data-user-info='" . htmlspecialchars(json_encode([
                    'utilizator' => $row['utilizator'],
                    'id_instanta' => $row['id_instanta'],
                    'id_sectie' => $row['idSectieInstanta'],
                    'id_rol' => $row['idRol']
                ])) . "'>
                <i class='fa fa-user-edit'></i>
            </button>

            <button class='btn btn-sm btn-outline-danger btn-inactivate-user'
                    data-bs-toggle='tooltip'
                    data-bs-html='true'
                    data-bs-placement='left'
                    title='Inactivează cont'
                    data-user-id='" . $row['id'] . "'
                    data-username='" . htmlspecialchars($row['utilizator']) . "'>
                <i class='fa fa-user-alt-slash'></i>
            </button>";
        }
    }

    $data[] = [
        'user' => $utilizatorNumeTD,
        'instanta' => $utilizatorInstanta,
        'rol' => $utilizatorRol,
        'actiuni' => $utilizatorAction
    ];
}

$response = [
    "draw" => $draw,
    "recordsTotal" => $totalRecords,
    "recordsFiltered" => $filteredRecords,
    "data" => $data,
];

header('Content-Type: application/json; charset=utf-8');
echo json_encode($response);