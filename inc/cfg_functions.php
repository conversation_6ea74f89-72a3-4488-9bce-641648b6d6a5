<?php
require_once __DIR__ . '/../controller/AccessControl.php';
date_default_timezone_set('Europe/Bucharest');
$zi_curenta = date("d");
$luna_curenta = date("m");
$an_curent = date("Y");
$ora_curenta = date("H");
$min_curent = date("i");
$sec_curenta = date("s");
$date = date("Y-m-d H:i:s");

$DOCUMENT_ROOT = $_SERVER['DOCUMENT_ROOT'];
$domain = $_SERVER['SERVER_NAME'];
$self = $_SERVER['PHP_SELF'];
$base_dir = __DIR__;
$folder_curent = getcwd();
$client_ip = $_SERVER['REMOTE_ADDR'];
$client_port = $_SERVER['REMOTE_PORT'];
$client_device = $_SERVER['HTTP_USER_AGENT'];
$client_device_tip = "D";


function data_afisare($data)
{
    if ($data == '0000-00-00' || $data == null || strlen($data) < 10) {
        return null;
    } elseif (strlen($data) == 10) {
        return date_format(date_create_from_format('Y-m-d', $data), 'd.m.Y');
    } elseif (strlen($data) > 10) {
        return date_format(date_create_from_format('Y-m-d H:i:s', $data), 'd.m.Y H:i:s');
    }
    return null;
}

function scurtare_data($data)
{
    if (strlen(data_null($data)) > 10) {
        return date_format(date_create_from_format('Y-m-d H:i:s', $data), 'Y-m-d');
    }
    if (data_null($data) == 10) {
        return $data;
    }
    return null;
}

function data_intrare_baza($data)
{
    //echo $data."<br>";
    if (containsWord($data, '_')) {
        return null;
    }
    if (strlen($data) == 10) {
        return date_format(date_create_from_format('d.m.Y', $data), 'Y-m-d');
    }
    return null;
}

function data_null($data)
{
    if ((strlen($data) == 10 || strlen($data) == 19) && $data[4] == '-' && $data[7] == '-' && $data[0] > 0) {
        return $data;
    }
    return null;
}

function getTelefon($telefon1, $telefon2)
{
    $telefon = "";
    $telefon1 = str_replace(['-', ':', ';', ","], " ", $telefon1);
    $telefon2 = str_replace(['-', ':', ';', ","], " ", $telefon2);

    if (empty($telefon1) and empty($telefon2)) {
        $telefon = "-";
    } else {
        if (!preg_match('/[a-zA-Z]/', $telefon1) and !empty($telefon1)) {
            $telefon = $telefon1;
        }
        if (!preg_match('/[a-zA-Z]/', $telefon2) and !empty($telefon2)) {

            $telefon .= "  ";
            $telefon .= $telefon2;
        }
    }

    return $telefon;
}

/**
 * Format phone numbers with icons and better styling for display in tables
 *
 * @param string $telefon1 First phone number
 * @param string $telefon2 Second phone number (optional)
 * @return string Formatted HTML for phone display
 */
function formatTelefon($telefon1, $telefon2)
{
    $telefon1 = trim($telefon1);
    $telefon2 = trim($telefon2);
    $output = "<div class='p-1'>";

    // No phone numbers available
    if (empty($telefon1) && empty($telefon2)) {
        $output .= "<div class='text-muted fst-italic'><i class='bi bi-dash-circle'></i> Telefon nedisponibil</div>";
    } else {
        // First phone number
        if (!empty($telefon1) && !preg_match('/[a-zA-Z]/', $telefon1)) {
            $output .= "<small><i class='bi bi-telephone'></i> $telefon1</small>";
        }

        // Second phone number (if available)
        if (!empty($telefon2) && !preg_match('/[a-zA-Z]/', $telefon2)) {
            $output .= "<small class='mt-1'><i class='bi bi-telephone'></i> $telefon2</small>";
        }
    }

    $output .= "</div>";
    return $output;
}

/**
 * Format workload (incarcatura) with visual indicators
 *
 * @param int|string $incarcatura The workload value
 * @return string Formatted HTML for workload display
 */
function formatIncarcatura($incarcatura)
{
    $incarcatura = intval($incarcatura);
    $output = "<div class='text-center p-1'>";

    // Determine color and icon based on workload value
    if ($incarcatura <= 3) {
        $color = 'success';
        $icon = 'check-circle-fill';
    } elseif ($incarcatura <= 7) {
        $color = 'warning';
        $icon = 'exclamation-triangle';
    } else {
        $color = 'danger';
        $icon = 'x-circle-fill';
    }

    // Create progress bar
    $percentage = min(100, ($incarcatura * 10));

    $output .= "<div class='progress' style='height: 10px;'>
                  <div class='progress-bar bg-$color' role='progressbar'
                       style='width: $percentage%;' aria-valuenow='$incarcatura'
                       aria-valuemin='0' aria-valuemax='10'></div>
                </div>";
    $output .= "<div class='small mt-1'>Valoare: $incarcatura</div>";

    $output .= "</div>";
    return $output;
}

function anonimizareCNP($cnp)
{
    if ($cnp == NULL) {
        return "CNP invalid";
    }
    $length = strlen($cnp);
    if ($length < 13) {
        return "CNP invalid";
    }
    $primeleTrei = substr($cnp, 0, 3);
    $anonimizat = $primeleTrei . str_repeat('X', $length - 3);
    return $anonimizat;
}

function validCNP($checkCNP)
{
    if (strlen($checkCNP) != 13) {
        return false;
    }
    if (in_array($checkCNP, ['1790929221143', '1670129381297'])) {
        return true;
    }
    $cnp = str_split($checkCNP);
    unset($checkCNP);
    $hashTable = array(2, 7, 9, 1, 4, 6, 3, 5, 8, 2, 7, 9);
    $hashResult = 0;

    // All characters must be numeric
    for ($i = 0; $i < 13; $i++) {
        if (!is_numeric($cnp[$i])) {
            return false;
        }
        $cnp[$i] = (int)$cnp[$i];
        if ($i < 12) {
            $hashResult += (int)$cnp[$i] * (int)$hashTable[$i];
        }
    }
    unset($hashTable, $i);
    $hashResult = $hashResult % 11;
    if ($hashResult == 10) {
        $hashResult = 1;
    }

    // Check Year
    $year = ($cnp[1] * 10) + $cnp[2];
    switch ($cnp[0]) {
        case 1  :
        case 2 :
            {
                $year += 1900;
            }
            break; // cetateni romani nascuti intre 1 ian 1900 si 31 dec 1999
        case 3  :
        case 4 :
            {
                $year += 1800;
            }
            break; // cetateni romani nascuti intre 1 ian 1800 si 31 dec 1899
        case 5  :
        case 6 :
            {
                $year += 2000;
            }
            break; // cetateni romani nascuti intre 1 ian 2000 si 31 dec 2099
        case 7  :
        case 8 :
        case 9 :
            {                // rezidenti si Cetateni Straini
                $year += 2000;
                if ($year > (int)date('Y') - 14) {
                    $year -= 100;
                }
            }
            break;
        default :
            {
                return false;
            }
            break;
    }
    return ($year > 1800 && $year < 2099 && $cnp[12] == $hashResult);
}

function sanitizeInput(string|array|null $input): string|array
{
    if (is_null($input)) {
        return ''; // or handle it as needed
    }

    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }

    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function injSql($ccc)
{
    $ccc = trim($ccc ?? '');

    $ccc = str_replace("%", "", $ccc ?? '');
    $ccc = str_replace("\"", "", $ccc ?? '');
    $ccc = str_replace("'", "", $ccc ?? '');
    $ccc = str_replace(";", "", $ccc ?? '');
    $ccc = str_replace("drop", "", $ccc);
    $ccc = str_replace("truncate", "", $ccc ?? '');
    $ccc = str_replace("delete", "", $ccc);
    $ccc = str_replace("table", "", $ccc ?? '');
    $ccc = str_replace("while", "", $ccc ?? '');
    $ccc = str_replace("update", "", $ccc);
    $ccc = str_replace("|", "", $ccc ?? '');
    $ccc = str_replace("=", "", $ccc ?? '');

    if (strlen($ccc) == 0) {
        $ccc = null;
    }
    return $ccc;
}

function getExp($v_IDjudete, $v_IDspec_IDsubspec, $cnpAlocatiList)
{
    require_once 'cfg_db.php';
    $pdo = DatabasePool::getConnection();

    $experti = [];

    $params = [];
    if (is_array($v_IDjudete)) {
        $judeteConditions = [];
        foreach ($v_IDjudete as $index => $judet) {
            $paramName = ":id_judet_$index";
            $judeteConditions[] = $paramName;
            $params[$paramName] = intval($judet);
        }
        $judeteSQL = implode(', ', $judeteConditions);
    } else {
        $params[":id_judet_0"] = intval($v_IDjudete);
        $judeteSQL = ":id_judet_0";
    }

    $conditions = [];
    foreach ($v_IDspec_IDsubspec as $index => $pair) {
        if (is_array($pair) && count($pair) == 2) { // Verificăm că este array valid
            $specializare = intval($pair[0]);
            $subspecializare = intval($pair[1]);

            $conditions[] = "(FIND_IN_SET(:spec_$index, e.specializari) > 0
                            AND FIND_IN_SET(:subspec_$index, e.subspecializari) > 0)
                            AND e.cnp NOT IN ($cnpAlocatiList)";

            $params["spec_$index"] = $specializare;
            $params["subspec_$index"] = $subspecializare;
        }
    }

    if (empty($conditions)) {
        return [];
    }

    $specializationCondition = implode(" AND ", $conditions);

    $sql = "
    SELECT e.nume,
           e.prenume,
           e.cnp,
           e.legitimatie,
           w.total as incarcatura,
           j.numeJudet judet,
           l.localitate localitate
    FROM exp_jud.experti_tehnici AS e
    LEFT JOIN workload AS W ON e.cnp = w.cnp
    join exp_jud.njudete j on j.idJudet = e.id_judet
    join exp_jud.z_localitati l on l.id = e.id_localitate
    WHERE ($specializationCondition)
      AND e.id_judet IN ($judeteSQL)
    GROUP BY e.cnp, e.nume, e.prenume,e.legitimatie, w.total;";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $row) {
        $experti[] = [
            'nume' => $row['nume'],
            'prenume' => $row['prenume'],
            'CNP' => $row['cnp'],
            'judet' => $row['judet'],
            'localitate' => $row['localitate'],
            'nr_legitimatie' => $row['legitimatie'],
            'incarcatura' => $row['incarcatura'] ?? 0
        ];
    }
    DatabasePool::releaseConnection($pdo);
    return $experti;
}

function lottery($idInstanta, $id_judet, $v_IDspec_IDsubspec, $nrExperti, $cnpAlocatiList)
{
    $dbConnection = DatabasePool::getConnection();
    $output = "Se caută în județul instanței...\n";

    $experti = getExp([$id_judet], $v_IDspec_IDsubspec, $cnpAlocatiList);

    $smtp = $dbConnection->prepare("CALL get_ID_superior_CA(:idInstanta)");
    $smtp->bindParam(':idInstanta', $idInstanta, PDO::PARAM_INT);
    $smtp->execute();
    $result = $smtp->fetchAll(PDO::FETCH_ASSOC);

    $idCA = isset($result[0]['id']) ? $result[0]['id'] : null;
    $smtp->closeCursor();

    if (count($experti) < $nrExperti) {
        $output .= "Niciun expert judiciar identificat în județ.\nSe caută în circumscripția curții de apel...\n";

        // 1 Call get_Judete_CA
        $stmt1 = $dbConnection->prepare("CALL get_Judete_CA(:idInstanta)");
        $stmt1->bindParam(':idInstanta', $idCA, PDO::PARAM_INT);
        $stmt1->execute();
        $result1 = $stmt1->fetchAll(PDO::FETCH_ASSOC);
        $stmt1->closeCursor();

        $vectorJudete = [];
        foreach ($result1 as $row) {
            $vectorJudete[] = $row['idJudet'];
        }

        $experti = getExp($vectorJudete, $v_IDspec_IDsubspec, $cnpAlocatiList);

        if (count($experti) < $nrExperti) {
            $output .= "Niciun expert judiciar identificat în circumscripția curții de apel.\nSe caută în circumscripțiile curților de apel învecinate...\n";

            // 2 Call get_vecini_CA
            $stmt2 = $dbConnection->prepare("CALL get_vecini_CA(:idInstanta)");
            $stmt2->bindParam(':idInstanta', $idCA, PDO::PARAM_INT);
            $stmt2->execute();
            $result2 = $stmt2->fetchAll(PDO::FETCH_ASSOC);
            $stmt2->closeCursor();

            foreach ($result2 as $row) {
                // 3 judetele din CA vecine
                $stmt3 = $dbConnection->prepare("CALL get_Judete_CA(:idInstanta)");
                $stmt3->bindParam(':idInstanta', $row['VECIN'], PDO::PARAM_INT);
                $stmt3->execute();

                $result3 = $stmt3->fetchAll(PDO::FETCH_ASSOC);
                $stmt3->closeCursor();

                foreach ($result3 as $row) {
                    $vectorJudete[] = $row['idJudet'];
                }
            }

            $experti = getExp($vectorJudete, $v_IDspec_IDsubspec, $cnpAlocatiList);

            if (count($experti) < $nrExperti) {
                $output .= "Niciun expert judiciar identificat în circumscripția curților de apel învecinate.\nSe caută la nivel național...\n";

                // 4 Search in all 42 counties + UE
                $vectorJudete = range(1, 43);
                $experti = getExp($vectorJudete, $v_IDspec_IDsubspec, $cnpAlocatiList);

                if (count($experti) < $nrExperti) {
                    $output .= "Niciun expert judiciar identificat la nivel național.\n";
                    DatabasePool::releaseConnection($dbConnection);
                    return [
                        'experti' => [],
                        'output' => trim($output)
                    ];
                }
            }
        }
    }

    DatabasePool::releaseConnection($dbConnection);
    if (count($experti) === $nrExperti) {
        return [
            'experti' => $experti,
            'output' => trim($output)
        ];
    }
    $incarcaturi = array_column($experti, 'incarcatura');
    $minIncarcatura = min($incarcaturi);

    $expertiFiltrati = array_filter($experti, function ($expert) use ($minIncarcatura) {
        return in_array($expert['incarcatura'], [$minIncarcatura, $minIncarcatura + 1, $minIncarcatura + 2], true);
    });

    $expertiFiltrati = array_values($expertiFiltrati);
    if (count($expertiFiltrati) < $nrExperti) {
        usort($experti, function ($a, $b) {
            return $a['incarcatura'] <=> $b['incarcatura'];
        });
        return [
            'experti' => array_slice($experti, 0, $nrExperti),
            'output' => trim($output)
        ];
    }
    shuffle($expertiFiltrati);
    return [
        'experti' => array_slice($expertiFiltrati, 0, $nrExperti),
        'output' => trim($output)
    ];
}

function getDefaultPageForRole($roleId)
{
    $rolePermissions = AccessControl::getRolePermissions();

    if (isset($rolePermissions[$roleId])) {
        return $rolePermissions[$roleId]['default_page'];
    }

    return 'index.php';
}

function getSpecializari()
{
    try {
        $dbConnection = DatabasePool::getConnection();

        $query = "SELECT * FROM exp_jud.specializarisubspecializari order by nume_specializare, nume_subspecializare;";
        $stmt = $dbConnection->query($query)->fetchAll();

        $data = [];
        foreach ($stmt as $row) {
//        $nume = mb_convert_encoding($row['nume'], 'UTF-8', 'ISO-8859-1');
            $data[] = [
                'id_specializare' => $row['id_specializare']
                , 'nume_specializare' => $row['nume_specializare']
                , 'id_subspecializare' => $row['id_subspecializare']
                , 'nume_subspecializare' => $row['nume_subspecializare']
            ];
        }
    } finally {
        DatabasePool::releaseConnection($dbConnection);
    }
    return $data;
}

function getSpecializariSelectOptgroup()
{
    $stmt = getSpecializari();

    $specializari = [];
    foreach ($stmt as $row) {
        $id_specializare = $row['id_specializare'];
        $nume_specializare = $row['nume_specializare'];
        $id_subspecializare = $row['id_subspecializare'];
        $nume_subspecializare = $row['nume_subspecializare'];

        if (!isset($specializari[$id_specializare])) {
            $specializari[$id_specializare] = [
                'nume' => $nume_specializare,
                'subspecializari' => []
            ];
        }

        if ($id_subspecializare) {
            $specializari[$id_specializare]['subspecializari'][] = [
                'id' => $id_subspecializare,
                'nume' => $nume_subspecializare
            ];
        }
    }

    $specializare = [];
    foreach ($specializari as $id_specializare => $spec) {
        if (empty($spec['subspecializari'])) {
            $nume = htmlspecialchars($spec['nume'], ENT_QUOTES, 'UTF-8');
            $specializare[] = "<option value='$id_specializare'>$nume</option>";
        } else {
            $nume = htmlspecialchars($spec['nume'], ENT_QUOTES, 'UTF-8');
            $specializare[] = "<optgroup label='$nume' id='$id_specializare'>";
            foreach ($spec['subspecializari'] as $sub) {
                $nume = htmlspecialchars($sub['nume'], ENT_QUOTES, 'UTF-8');
                $specializare[] = "<option value='$sub[id]'>$nume</option>";
            }
            $specializare[] = '</optgroup>';
        }
    }

    $specializare = implode('', $specializare);
    return $specializare;
}

function getSpecializariExpertDAETJ($cnp)
{
    require_once 'cfg_db.php';
    $dbConnection = DatabasePool::getConnection();

    $select = "select COALESCE(e.specializari, '0') specializari, COALESCE(e.subspecializari, '0') subspecializari from exp_jud.experti_tehnici e where e.cnp = '$cnp';";
    $select = $dbConnection->query($select)->fetch(PDO::FETCH_ASSOC);
    $specializari = explode(',', $select['specializari']);
    $subspecializari = explode(',', $select['subspecializari']);

    DatabasePool::releaseConnection($dbConnection);

    return [
        'specializari' => $specializari,
        'subspecializari' => $subspecializari
    ];
}

function checkExpertAlocat($nr_dosar_national, $v_IDspec_IDsubspec)
{
    // cauta in tabela expertize daca exista experti alocati pentru dosarul respectiv pe specializarea și subspecializare cautată
    require_once 'cfg_db.php';
    $pdo = DatabasePool::getConnection();
    $conditions = [];
    foreach ($v_IDspec_IDsubspec as $index => $pair) {
        if (is_array($pair) && count($pair) == 2) { // Verificăm că este array valid
            $specializare = intval($pair[0]);
            $subspecializare = intval($pair[1]);

            $conditions[] = "(FIND_IN_SET(:spec_$index, ex.idSpecializare) > 0
                            AND FIND_IN_SET(:subspec_$index, ex.idSubspecializare) > 0
                            AND ex.nrDosar = :nr_dosar_national
                            AND ex.IdStatusExpertiza in (1,2,3))";

            $params["spec_$index"] = $specializare;
            $params["subspec_$index"] = $subspecializare;
            $params["nr_dosar_national"] = $nr_dosar_national;
        }
    }

    if (empty($conditions)) {
        return [];
    }
    $specializationCondition = implode(" AND ", $conditions);

    $sql = "
    SELECT
    ex.id,
    ex.idSpecializare,
    ex.idSubspecializare,
    e.nume,
    e.prenume,
    e.cnp,
    e.legitimatie
    FROM exp_jud.experti_tehnici AS e
    inner join exp_jud.expertize as ex
    on ex.cnpExpert=e.cnp
    WHERE ($specializationCondition);";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $expertAlocat = [];
    foreach ($results as $row) {
        $expertAlocat[] = [
            'id' => $row['id'],
            'idSpecializare' => $row['idSpecializare'],
            'idSubspecializare' => $row['idSubspecializare'],
            'nume' => $row['nume'],
            'prenume' => $row['prenume'],
            'CNP' => $row['cnp'],
            'nr_legitimatie' => $row['legitimatie']
        ];
    }
    DatabasePool::releaseConnection($pdo);
    return $expertAlocat;
}

function ldapsConnectToServers()
{
    $servers = ['ldaps://10.1.244.174:636', 'ldaps://10.1.245.80:636', 'ldaps://10.1.245.79:636'];

    foreach ($servers as $server) {
        $ldap_conn = ldap_connect($server);
        if ($ldap_conn) {
            ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
            ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);

            return $ldap_conn;
        }
    }
    return false;
}

function requestADCred($username, $password)
{
    if (extension_loaded('ldap')) {
        $DomainName = 'just.ro'; // name = domain
        $auth_user = $username . "@" . $DomainName;

        $connect = ldapsConnectToServers();
        if ($bind = @ldap_bind($connect, $auth_user, $password)) {
            @ldap_close($connect);
            return 1;
        } else {
            @ldap_close($connect);
            return "Credențiale invalide.";
        }
    }
    return "LDAPS module not loaded";
}

function checkExistUserDB($username)
{
    require_once 'inc/cfg_db.php';
    $dbConnection = DatabasePool::getConnection();

    $username = strtolower(sanitizeInput($username));
    $querry = "SELECT
               u.id as idUtilizator,
               u.utilizator as username,
               r.id as idRol,
               r.tip as rol,
               i.id as id_instanta,
               u.idSectieInstanta as id_sectie,
               i.den as instanta,
               j.numeJudet as judet,
               j.idJudet as idJudet,
               i.id_nivel as nivelInstanta
             FROM exp_jud.utilizatori u
             JOIN exp_jud.z_rol_utilizator r on r.id=u.idRol
             JOIN exp_jud.z_instante i on i.id=u.idInstanta
             JOIN exp_jud.njudete j on j.idJudet=i.id_judet
             where utilizator=:utilizator and u.dataInactivare is null";

    try {
        $stmt = $dbConnection->prepare($querry);
        $stmt->execute(['utilizator' => $username]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in checkExistUserDB: " . $e->getMessage()
        ]);
        return null;
    }
}

function checkCredentials()
{
    global $sesiune;
    $username = strtolower(trim(sanitizeInput($_POST["username"])));
    $username = getUsername($username);
    $password = sanitizePassword($_POST["password"]);

    // Get database connection for audit logging
    $dbConnection = DatabasePool::getConnection();

    $userDB = checkExistUserDB($username);
    if ($userDB != null) {//exista in baza
        $return = requestADCred($username, $password);
        if ($return == 1) {
            // Successful login - log the event
            log_auth_event($dbConnection, 'login', [
                'user_id' => $userDB['idUtilizator'],
                'username' => $username
            ]);

            $sesiune->set('logged_in', true);
            $sesiune->set('username', $userDB['username']);
            $sesiune->set('instanta', $userDB['instanta']);
            $sesiune->set('id_instanta', $userDB['id_instanta']);
            $sesiune->set('id_sectie', $userDB['id_sectie']);
            $sesiune->set('judet', $userDB['judet']);
            $sesiune->set('id_rol', $userDB['idRol']);
            $sesiune->set('rol', $userDB['rol']);
            $sesiune->set('parolaUser', $password);
            $sesiune->set('id_utilizator', $userDB['idUtilizator']);
            $sesiune->set('id_judet', $userDB['idJudet']);
            $sesiune->set('id_nivelInstanta', $userDB['nivelInstanta']);
            $sesiune->set('last_activity', time()); // Set initial last activity time

            $redirectLocation = getDefaultPageForRole($userDB['idRol']);
            header("Location: $redirectLocation");
            exit();
        } else {
            // Failed login - log the event
            log_auth_event($dbConnection, 'login_failed', [
                'description' => "Autentificare eșuată: $return - username $username"
            ]);

            return $return;
        }
    } else {
        // User not found - log the event
        log_auth_event($dbConnection, 'login_failed', [
            'description' => "Utilizator inexistent în baza de date - username $username"
        ]);

        return "Cont neînrolat - contactați compartimentul informatic pentru înrolare și atribuire rol";
    }
}

function sanitizePassword(mixed $password)
{
    if (empty($password)) {
        return '';
    }

    // Elimină caractere de control și whitespace de la capete
    $password = trim($password);

    // Verifică lungimea maximă pentru a preveni atacurile de tip buffer overflow
    if (strlen($password) > 128) {
        throw new Exception("Parola este prea lungă!");
    }

    return $password;
}

function checkLDAPUserExists($userName)
{
    global $env;
    $messageLDAP = $statusLDAP = null;

    $ldap_dn = "DC=just,DC=ro";
    $ldap_conn = ldapsConnectToServers();

    $adminUser = $env["ADMIN_AD_USER"];
    $adminPass = $env["ADMIN_AD_PASS"];
    if (ldap_bind($ldap_conn, $adminUser, $adminPass)) {
//        $search_filter = "(cn=$userName)";
//        $search_filter = "(mail=$<EMAIL>)";
        if (strlen($userName))
            $userNameSearch = substr($userName, 0, 20);
        else
            $userNameSearch = $userName;
        $search_filter = "(sAMAccountName=$userNameSearch)";
        $attributes = array("cn", "sn", "userprincipalname", "mail", "sAMAccountName", "useraccountcontrol");
        $search_result = ldap_search($ldap_conn, $ldap_dn, $search_filter, $attributes);

        if (!$search_result) {
            $messageLDAP = "LDAP search failed.";
            $statusLDAP = 'error';
        }
        $entries = ldap_get_entries($ldap_conn, $search_result);
//        $messageLDAP = var_dump($entries);

        if ($entries["count"] > 0) {
            $messageLDAP = "Userul exista in AD!";
            $statusLDAP = 'ok';
        } else {
            $messageLDAP = "Utilizatorul <b>$userName</b> nu există în domeniul @just.ro";
            $statusLDAP = 'error';
        }
    } else {
        $messageLDAP = "Failed to bind to LDAP server.";
        $statusLDAP = 'error';
    }
    ldap_close($ldap_conn);

    return [
        'messageLDAP' => $messageLDAP
        , 'statusLDAP' => $statusLDAP
    ];

}

function getUsername($username)
{

    if (strpos($username, '@just.ro') === 0)
        return $username;
    else
        return str_replace('@just.ro', '', $username);

}

/**
 * Logs an audit action to the database
 *
 * @param PDO $pdo Database connection
 * @param array $params Parameters for the audit log
 * @return void
 */
function log_audit_action(PDO $pdo, array $params = []): void
{
    $defaults = [
        'user_id' => isset($GLOBALS['SESSION_id_utilizator']) ? $GLOBALS['SESSION_id_utilizator'] : null,
        'session_id' => session_id(),
        'action_type' => 'unknown',
        'action_category' => null,
        'action_level' => 'info',
        'table_name' => null,
        'record_id' => null,
        'description' => '',
        'data_before' => null,
        'data_after' => null,
        'url' => $_SERVER['REQUEST_URI'] ?? null,
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN'
    ];

    $data = array_merge($defaults, $params);

    $stmt = $pdo->prepare("
        INSERT INTO audit_log (
            user_id, session_id, action_type, action_category, action_level,
            table_name, record_id, url, request_method,
            description, data_before, data_after,
            ip_address, user_agent
        ) VALUES (
            :user_id, :session_id, :action_type, :action_category, :action_level,
            :table_name, :record_id, :url, :request_method,
            :description, :data_before, :data_after,
            :ip_address, :user_agent
        )
    ");

    $stmt->execute([
        ':user_id' => $data['user_id'],
        ':session_id' => $data['session_id'],
        ':action_type' => $data['action_type'],
        ':action_category' => $data['action_category'],
        ':action_level' => $data['action_level'],
        ':table_name' => $data['table_name'],
        ':record_id' => $data['record_id'],
        ':url' => $data['url'],
        ':request_method' => $data['request_method'],
        ':description' => $data['description'],
        ':data_before' => is_array($data['data_before']) ? json_encode($data['data_before']) : $data['data_before'],
        ':data_after' => is_array($data['data_after']) ? json_encode($data['data_after']) : $data['data_after'],
        ':ip_address' => $data['ip_address'],
        ':user_agent' => $data['user_agent']
    ]);
}

/**
 * Logs authentication events (login, logout, failed login)
 *
 * @param PDO $pdo Database connection
 * @param string $action_type Type of authentication action (login, logout, login_failed)
 * @param array $details Additional details about the authentication event
 * @return void
 */
function log_auth_event(PDO $pdo, string $action_type, array $details = []): void
{
    $user_id = $details['user_id'] ?? null;
    $username = $details['username'] ?? null;
    $description = $details['description'] ?? '';

    // Set action level based on action type
    $action_level = 'info';
    if ($action_type === 'login_failed') {
        $action_level = 'warning';
    }

    // Build description if not provided
    if (empty($description)) {
        switch ($action_type) {
            case 'login':
                $description = "Autentificare reușită pentru utilizatorul: $username";
                break;
            case 'logout':
                $description = "Delogare pentru utilizatorul: $username";
                break;
            case 'login_failed':
                $description = "Încercare eșuată de autentificare pentru utilizatorul: $username";
                break;
            case 'session_expired':
                $description = "Sesiune expirată pentru utilizatorul: $username";
                break;
            default:
                $description = "Eveniment de autentificare: $action_type";
        }
    }

    log_audit_action($pdo, [
        'user_id' => $user_id,
        'action_type' => $action_type,
        'action_category' => 'authentication',
        'action_level' => $action_level,
        'description' => "{{$description}}, {ip_address: {$_SERVER['REMOTE_ADDR']}}"
    ]);
}

/**
 * Logs data operations (CRUD actions)
 *
 * @param PDO $pdo Database connection
 * @param string $action_type Type of data operation (insert, update, delete, view)
 * @param string $table_name Name of the affected table
 * @param int|null $record_id ID of the affected record
 * @param array $details Additional details about the operation
 * @return void
 */
function log_data_operation(PDO $pdo, string $action_type, string $table_name, ?int $record_id = null, array $details = []): void
{
    $description = $details['description'] ?? '';
    $data_before = $details['data_before'] ?? null;
    $data_after = $details['data_after'] ?? null;

    // Set action level based on action type
    $action_level = 'info';
    if ($action_type === 'delete') {
        $action_level = 'warning';
    }

    // Build description if not provided
    if (empty($description)) {
        switch ($action_type) {
            case 'insert':
                $description = "Adăugare înregistrare în tabela $table_name";
                break;
            case 'update':
                $description = "Actualizare înregistrare în tabela $table_name" . ($record_id ? " (ID: $record_id)" : '');
                break;
            case 'delete':
                $description = "Ștergere înregistrare din tabela $table_name" . ($record_id ? " (ID: $record_id)" : '');
                break;
            case 'view':
                $description = "Vizualizare înregistrare din tabela $table_name" . ($record_id ? " (ID: $record_id)" : '');
                break;
            default:
                $description = "Operație $action_type pe tabela $table_name" . ($record_id ? " (ID: $record_id)" : '');
        }
    }

    log_audit_action($pdo, [
        'action_type' => $action_type,
        'action_category' => 'data_operation',
        'action_level' => $action_level,
        'table_name' => $table_name,
        'record_id' => $record_id,
        'description' => $description,
        'data_before' => $data_before,
        'data_after' => $data_after
    ]);
}

/**
 * Logs administrative actions
 *
 * @param PDO $pdo Database connection
 * @param string $action_type Type of administrative action
 * @param array $details Additional details about the action
 * @return void
 */
function log_admin_action(PDO $pdo, string $action_type, array $details = []): void
{
    $description = $details['description'] ?? '';
    $affected_user_id = $details['affected_user_id'] ?? null;
    $table_name = $details['table_name'] ?? null;
    $record_id = $details['record_id'] ?? null;
    $data_before = $details['data_before'] ?? null;
    $data_after = $details['data_after'] ?? null;

    // Set action level based on action type
    $action_level = 'info';
    if (in_array($action_type, ['user_deactivate', 'role_change', 'permission_change', 'user_delete', 'user_unasign'])) {
        $action_level = 'warning';
    }

    log_audit_action($pdo, [
        'action_type' => $action_type,
        'action_category' => 'administrative',
        'action_level' => $action_level,
        'table_name' => $table_name,
        'record_id' => $record_id,
        'description' => $description,
        'data_before' => $data_before,
        'data_after' => $data_after
    ]);
}

/**
 * Logs page access events
 *
 * @param PDO $pdo Database connection
 * @param string $page_name Name of the accessed page
 * @param array $details Additional details about the access
 * @return void
 */
function log_page_access(PDO $pdo, string $page_name, array $details = []): void
{
    $description = $details['description'] ?? "Acces la pagina: $page_name";
    $query_params = $details['query_params'] ?? null;

    // If we have query parameters, add them to the data_after field as JSON
    $data_after = null;
    if ($query_params) {
        $data_after = json_encode($query_params);
    }

    log_audit_action($pdo, [
        'action_type' => 'page_access',
        'action_category' => 'navigation',
        'action_level' => 'info',
        'description' => $description,
        'data_after' => $data_after
    ]);
}

/**
 * Logs search and filter operations
 *
 * @param PDO $pdo Database connection
 * @param string $search_type Type of search (e.g., 'expert_search', 'case_search')
 * @param array $search_params Search parameters used
 * @param array $details Additional details about the search
 * @return void
 */
function log_search_operation(PDO $pdo, string $search_type, array $search_params, array $details = []): void
{
    $description = $details['description'] ?? "Căutare: $search_type";

    log_audit_action($pdo, [
        'action_type' => 'search',
        'action_category' => 'data_access',
        'action_level' => 'info',
        'description' => $description,
        'data_after' => json_encode($search_params)
    ]);
}


/**
 * Anonymizes a string by replacing a specified percentage of characters with asterisks
 *
 * @param string $str The string to anonymize
 * @param int $anonymizePercent The percentage of characters to anonymize (1-100)
 * @param bool $checkSqlKeywords Whether to check for SQL keywords to force anonymization
 * @param int $minLength Minimum string length to consider for anonymization
 * @return string The anonymized string
 */
function anonymizeString($str, $anonymizePercent = 80, $checkSqlKeywords = true, $minLength = 20)
{
    if (empty($str)) return $str;

    // Ensure percentage is between 1 and 100
    $anonymizePercent = max(1, min(100, $anonymizePercent));

    // Check if the string is long enough to warrant anonymization
    // Also check for SQL keywords as a definite trigger for anonymization
    $shouldAnonymize = false;

    // Anonymize if string is longer than minimum length
    if (strlen($str) > $minLength) {
        $shouldAnonymize = true;
    }

    // Check for SQL keywords if enabled
    if ($checkSqlKeywords) {
        $sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'ALTER', 'CREATE', 'sql'];
        foreach ($sqlKeywords as $keyword) {
            if (stripos($str, $keyword) !== false) {
                $shouldAnonymize = true;
                break;
            }
        }
    }

    if ($shouldAnonymize) {
        // Calculate how many characters to preserve
        $length = strlen($str);
        $preservePercent = 100 - $anonymizePercent;
        $preserveCount = max(1, (int)($length * $preservePercent / 100)); // Ensure at least 1 character is preserved
        $preservePositions = [];

        // Randomly select positions to preserve
        for ($i = 0; $i < $preserveCount; $i++) {
            $preservePositions[] = rand(0, $length - 1);
        }

        $result = '';
        for ($i = 0; $i < $length; $i++) {
            if (in_array($i, $preservePositions)) {
                $result .= $str[$i];
            } else {
                $result .= '*';
            }
        }
        return $result;
    }

    return $str;
}


/**
 * Generează un text formatat cu numele specializărilor și subspecializărilor
 * @param array $specializari Array cu ID-urile specializărilor
 * @param array $subSpecializari Array cu ID-urile subspecializărilor
 * @param array|null $specializariAlese Array opțional cu numele specializărilor deja selectate
 * @return string Text formatat cu specializările și subspecializările
 */
function getTextSpecializari($specializari, $subSpecializari, $specializariAlese = null)
{
    $textSpecializari = "";

    // Dacă avem deja numele specializărilor, le folosim
    if (!empty($specializariAlese)) {
        $textSpecializari = implode(", ", $specializariAlese);
    } else {
        // Altfel, obținem numele din baza de date folosind ID-urile
        $sql = getSpecializari();
        $specializariNames = [];

        // Adăugăm numele specializărilor
        foreach ($specializari as $specId) {
            foreach ($sql as $row) {
                if ($specId == $row['id_specializare']) {
                    $specializariNames[] = $row['nume_specializare'];
                    break;
                }
            }
        }

        // Adăugăm numele subspecializărilor (dacă există)
        foreach ($subSpecializari as $subSpecId) {
            if ($subSpecId > 0) { // Ignorăm valorile 0
                foreach ($sql as $row) {
                    if ($subSpecId == $row['id_subspecializare']) {
                        $specializariNames[] = $row['nume_subspecializare'];
                        break;
                    }
                }
            }
        }

        $textSpecializari = implode(", ", $specializariNames);
    }

    return $textSpecializari;
}

function verificaExistentaInWorkload($CNP_Random)
{
    global $dbConnection;

    $existaInWorkloadOriBa = "SELECT * FROM exp_jud.workload WHERE CNP IN ($CNP_Random) AND idLoad > 0";
    $existaInWorkloadOriBa = $dbConnection->query($existaInWorkloadOriBa)->fetchAll(PDO::FETCH_ASSOC);
    if (count($existaInWorkloadOriBa) == 0) {
        $insertAdaugaInWorkload = "INSERT INTO exp_jud.workload (CNP, incarcatura_ETJ, incarcatura_app) VALUES ";
        $cnpArray = explode(',', $CNP_Random);
        foreach ($cnpArray as $cnpPrelucrat) {
            $cnpPrelucrat = trim($cnpPrelucrat, "'");
            $insertAdaugaInWorkload .= "('$cnpPrelucrat', 0, 0),";
        }
        $insertAdaugaInWorkload = rtrim($insertAdaugaInWorkload, ',');

        $dbConnection->exec($insertAdaugaInWorkload);
        $idWorkloadExpertiza = $dbConnection->lastInsertId();

        log_data_operation($dbConnection, 'insert', 'workload', $idWorkloadExpertiza, [
            'description' => "Adăugare expert in workload (intrucat e inexistent in momentul acesta) ca sa pot sa ii fac incrementari/decrementari.",
            'data_after' => [
                'cnpExpert' => $CNP_Random,
                'sql' => $insertAdaugaInWorkload
            ]
        ]);

    }
}


function writeToLogFile($text, $fileName = null)
{
    global $date;
    ($fileName == null) ? $fileName = __DIR__ . "/../logs/logDAETJ-" . scurtare_data($date) . ".log" : $fileName = __DIR__ . "/../logs/logDAETJ-$fileName-" . scurtare_data($date);

    file_put_contents($fileName, "\n$text", FILE_APPEND | LOCK_EX);
}

require_once __DIR__ . '/EmailService.php';

function getExpertData($cnp){
    $dbConnection = DatabasePool::getConnection();
    $sql = "select e.cnp, e.nume, e.prenume, e.legitimatie nr_legitimatie, j.numeJudet judet, e.id_judet, l.localitate
            from experti_tehnici e join njudete j on j.idJudet = e.id_judet
            join z_localitati l on l.id = e.id_localitate WHERE cnp = :cnp";
    $stmt = $dbConnection->prepare($sql);
    $stmt->execute([':cnp' => $cnp]);
    $expertData = $stmt->fetch(PDO::FETCH_ASSOC);
    DatabasePool::releaseConnection($dbConnection);
    return $expertData;
}

function formatNumarTelefon($nrTelefon)
{
    // sterge spatii, bara, paranteze
    $nrTelefon = preg_replace('/[\s\-()]/', '', $nrTelefon);

    // If the number starts with +407 or 00407, replace with 07
    if (preg_match('/^(\+|00)407/', $nrTelefon)) {
        $nrTelefon = preg_replace('/^(\+|00)407/', '07', $nrTelefon);
    }

    // Check if the number is now in the correct format 07xxxxxxxx
    if (preg_match('/^07\d{8}$/', $nrTelefon)) {
        return "+4$nrTelefon";
    } else {
        return false;
    }
}
